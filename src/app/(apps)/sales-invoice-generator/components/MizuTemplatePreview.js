import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import Mustache from "mustache";
import parse from "html-react-parser";

const MizuTemplatePreview = ({
  selectedTemplate,
  companyData,
  onBack,
  onProceed,
  variants
}) => {
  const [renderedHtml, setRenderedHtml] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Static data for preview
  const staticInvoiceData = {
    invoice: {
      number: "INV-2024-001",
      date: "2024-01-15",
      due_date: "2024-02-14",
      po_number: "PO-12345",
      subtotal: 2500.00,
      tax_rate: 8.5,
      tax_amount: 212.50,
      discount_amount: 0,
      total: 2712.50,
      items: [
        {
          description: "Web Development Services",
          quantity: 40,
          rate: 50.00,
          amount: 2000.00
        },
        {
          description: "UI/UX Design Consultation",
          quantity: 10,
          rate: 75.00,
          amount: 750.00
        }
      ]
    },
    client: {
      name: "Acme Corporation",
      address: "456 Client Street, Business City, BC 12345",
      email: "<EMAIL>",
      phone: "+****************"
    },
    company: {
      ...companyData,
      logo: companyData.logo ? URL.createObjectURL(companyData.logo) : null
    }
  };

  useEffect(() => {
    if (selectedTemplate && companyData) {
      renderTemplate();
    }
  }, [selectedTemplate, companyData]);

  // Convert Django/Jinja2 template syntax to Mustache syntax
  const convertToMustacheTemplate = (template) => {
    let converted = template;

    // Convert loops: {% for item in invoice.items %} to {{#invoice.items}}
    converted = converted.replace(/{% for item in invoice\.items %}/g, '{{#invoice.items}}');
    converted = converted.replace(/{% endfor %}/g, '{{/invoice.items}}');

    // Convert item references inside loops: {{ item.property }} to {{ property }}
    converted = converted.replace(/{{ item\./g, '{{ ');

    // Convert filters: {{ value|floatformat:2 }} to {{ value_formatted }}
    converted = converted.replace(/{{ ([^|]+)\|floatformat:2 }}/g, '{{ $1_formatted }}');

    // Convert default filters: {{ value|default:"text" }} to {{#value}}{{value}}{{/value}}{{^value}}text{{/value}}
    converted = converted.replace(/{{ ([^|]+)\|default:"([^"]+)" }}/g, '{{#$1}}{{$1}}{{/$1}}{{^$1}}$2{{/$1}}');

    // Handle complex conditional blocks with nested content
    // First, handle {% else %} blocks by converting them to inverted sections
    converted = converted.replace(/{% else %}/g, '{{/TEMP_CONDITION}}{{^TEMP_CONDITION}}');

    // Handle {% if not condition %} blocks
    converted = converted.replace(/{% if not ([^%]+) %}/g, '{{^$1}}');

    // Handle regular {% if condition %} blocks
    const ifRegex = /{% if ([^%]+) %}([\s\S]*?){% endif %}/g;
    let match;
    const replacements = [];

    while ((match = ifRegex.exec(converted)) !== null) {
      const condition = match[1].trim();
      let content = match[2];

      // Handle else blocks within this if block
      if (content.includes('{{/TEMP_CONDITION}}{{^TEMP_CONDITION}}')) {
        const parts = content.split('{{/TEMP_CONDITION}}{{^TEMP_CONDITION}}');
        const ifContent = parts[0];
        const elseContent = parts[1];

        const replacement = `{{#${condition}}}${ifContent}{{/${condition}}}{{^${condition}}}${elseContent}{{/${condition}}}`;
        replacements.push({
          original: match[0],
          replacement: replacement
        });
      } else {
        const replacement = `{{#${condition}}}${content}{{/${condition}}}`;
        replacements.push({
          original: match[0],
          replacement: replacement
        });
      }
    }

    // Apply all replacements
    replacements.forEach(({ original, replacement }) => {
      converted = converted.replace(original, replacement);
    });

    // Clean up any remaining TEMP_CONDITION markers
    converted = converted.replace(/{{\/TEMP_CONDITION}}{{[\^#]TEMP_CONDITION}}/g, '');

    return converted;
  };

  // Prepare data for Mustache rendering
  const prepareDataForMustache = (data) => {
    const prepared = JSON.parse(JSON.stringify(data)); // Deep clone

    // Add formatted versions of numeric fields
    if (prepared.invoice) {
      if (prepared.invoice.subtotal) {
        prepared.invoice.subtotal_formatted = prepared.invoice.subtotal.toFixed(2);
      }
      if (prepared.invoice.tax_amount) {
        prepared.invoice.tax_amount_formatted = prepared.invoice.tax_amount.toFixed(2);
      }
      if (prepared.invoice.discount_amount) {
        prepared.invoice.discount_amount_formatted = prepared.invoice.discount_amount.toFixed(2);
      }
      if (prepared.invoice.total) {
        prepared.invoice.total_formatted = prepared.invoice.total.toFixed(2);
      }

      // Format items
      if (prepared.invoice.items) {
        prepared.invoice.items = prepared.invoice.items.map(item => ({
          ...item,
          rate_formatted: (item.rate || 0).toFixed(2),
          amount_formatted: (item.amount || 0).toFixed(2)
        }));
      }
    }

    return prepared;
  };

  // Mustache template renderer function
  const renderTemplate = async () => {
    try {
      setLoading(true);
      setError("");

      // Convert Django template to Mustache template
      const mustacheTemplate = convertToMustacheTemplate(selectedTemplate.html_content);

      // Prepare data for Mustache
      const mustacheData = prepareDataForMustache(staticInvoiceData);

      // Render with Mustache
      const rendered = Mustache.render(mustacheTemplate, mustacheData);
      setRenderedHtml(rendered);
    } catch (err) {
      console.error("Template rendering error:", err);
      setError("Failed to render template: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleProceed = () => {
    // Pass the rendered data to the next step
    onProceed({
      template: selectedTemplate,
      companyData: companyData,
      staticData: staticInvoiceData,
      renderedHtml: renderedHtml
    });
  };

  if (loading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600">Rendering template...</p>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center py-12"
        variants={variants}
      >
        <div className="text-red-600 text-center">
          <p className="text-lg font-medium mb-2">Template Rendering Error</p>
          <p className="text-sm mb-4">{error}</p>
          <button
            onClick={renderTemplate}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-2"
          >
            Try Again
          </button>
          <button
            onClick={onBack}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-medium text-gray-700">
            Template Preview
          </h2>
          <p className="text-gray-600 mt-1">
            Preview of {selectedTemplate.name} template with your company data
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={onBack}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back
          </button>
          <button
            onClick={handleProceed}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>

      {/* Template Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-blue-900">{selectedTemplate.name}</h3>
            <p className="text-sm text-blue-700">{selectedTemplate.description}</p>
          </div>
        </div>
      </div>

      {/* Preview Container */}
      <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Invoice Preview</span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>Sample data is used for demonstration</span>
            </div>
          </div>
        </div>

        <div className="p-6 max-h-96 overflow-y-auto">
          {renderedHtml && (
            <div className="invoice-preview" style={{
              fontSize: '12px',
              lineHeight: '1.4',
              transform: 'scale(0.8)',
              transformOrigin: 'top left',
              width: '125%'
            }}>
              {parse(renderedHtml)}
            </div>
          )}
        </div>
      </div>

      {/* Data Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-700 mb-2">Your Company Data</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Name:</strong> {companyData.company_name}</p>
            <p><strong>Email:</strong> {companyData.email}</p>
            <p><strong>Phone:</strong> {companyData.phone}</p>
            <p><strong>Address:</strong> {companyData.address_line_1}</p>
            {companyData.logo && <p><strong>Logo:</strong> Uploaded</p>}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-700 mb-2">Sample Invoice Data</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Invoice #:</strong> {staticInvoiceData.invoice.number}</p>
            <p><strong>Client:</strong> {staticInvoiceData.client.name}</p>
            <p><strong>Total:</strong> ${staticInvoiceData.invoice.total.toFixed(2)}</p>
            <p><strong>Items:</strong> {staticInvoiceData.invoice.items.length} line items</p>
          </div>
        </div>
      </div>

      {/* Note */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This is a preview with sample data. In the actual invoice generation,
              you'll be able to customize all the invoice details, line items, and client information.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default MizuTemplatePreview;
