from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from rest_framework.decorators import api_view
from rest_framework.renderers import StaticHTMLRenderer
from rest_framework.response import Response
from rest_framework.views import APIView
import os
from django.conf import settings

# Create your views here


@api_view(["POST"])
def extract_template_information(request):
    """
    Endpoint to extract information from an uploaded invoice PDF file.
    Returns dummy data for now.
    """
    if "file" not in request.FILES:
        return JsonResponse({"error": "No PDF file provided"}, status=400)

    pdf_file = request.FILES["file"]

    # Here we would process the PDF, but for now we return dummy data
    invoice_data = {
        "company_name": "Example Company Ltd",
        "address": "123 Business Street, City, Country",
        "contact_email": "<EMAIL>",
        "payment_terms": "Net 30 days",
        "bank_info": "Bank: Example Bank, Account: ********, Sort Code: 01-02-03",
        "company_logo": "base64_encoded_image_data_would_be_here",
        "Template Name": "",
    }

    return JsonResponse(invoice_data)


class InvoiceTemplateListView(APIView):
    """
    API endpoint to fetch all available invoice templates.
    Returns a list of templates with their metadata and raw HTML content.
    """

    def get(self, request):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        templates = []

        # Template metadata
        template_info = {
            "clean_business.html": {
                "name": "Clean Business",
                "description": "A clean and professional business invoice template",
                "preview_image": "/static/template-previews/clean_business.png",
            },
            "corporate.html": {
                "name": "Corporate",
                "description": "A formal corporate invoice template with traditional styling",
                "preview_image": "/static/template-previews/corporate.png",
            },
            "minimalist.html": {
                "name": "Minimalist",
                "description": "A simple and elegant minimalist invoice template",
                "preview_image": "/static/template-previews/minimalist.png",
            },
            "elegant_classic.html": {
                "name": "Elegant Classic",
                "description": "An elegant classic invoice template with decorative elements",
                "preview_image": "/static/template-previews/elegant_classic.png",
            },
            "contemporary.html": {
                "name": "Contemporary",
                "description": "A modern contemporary invoice template with vibrant colors",
                "preview_image": "/static/template-previews/contemporary.png",
            },
        }

        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(".html"):
                    file_path = os.path.join(templates_dir, filename)
                    with open(file_path, "r", encoding="utf-8") as file:
                        html_content = file.read()

                    template_data = {
                        "id": filename.replace(".html", ""),
                        "filename": filename,
                        "html_content": html_content,
                        **template_info.get(
                            filename,
                            {
                                "name": filename.replace(".html", "")
                                .replace("_", " ")
                                .title(),
                                "description": f"Invoice template: {filename}",
                                "preview_image": "/static/template-previews/default.png",
                            },
                        ),
                    }
                    templates.append(template_data)

            return Response(
                {"success": True, "templates": templates, "count": len(templates)}
            )

        except Exception as e:
            return Response({"success": False, "error": str(e)}, status=500)


class InvoiceTemplateDetailView(APIView):
    """
    API endpoint to fetch a specific invoice template by ID.
    Returns the raw HTML content using StaticHTMLRenderer.
    """

    renderer_classes = [StaticHTMLRenderer]

    def get(self, request, template_id):
        templates_dir = os.path.join(settings.BASE_DIR, "invoice_templates")
        template_file = f"{template_id}.html"
        file_path = os.path.join(templates_dir, template_file)

        try:
            if not os.path.exists(file_path):
                return Response(
                    "<html><body><h1>Template not found</h1></body></html>", status=404
                )

            with open(file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            return Response(html_content)

        except Exception as e:
            return Response(
                f"<html><body><h1>Error loading template: {str(e)}</h1></body></html>",
                status=500,
            )
