"""
URL configuration for MizuFlow project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.conf import settings
from django.urls import path, include
from . import views

urlpatterns = [
    path("admin/", admin.site.urls),
    path("", views.index, name="index"),
    path("api/v1/users/", include("apps.users.urls")),
    path("api/v1/invoice-automation/", include("apps.invoice_automation.urls")),
    path("api/v1/tax-acc-assistant/", include("apps.tax_acc_assistant.urls")),
    path("api/v1/credit-balance/", include("apps.credit_balance.urls")),
    path("api/v1/payments/", include("apps.payments.urls")),
    path("api/v1/xero/", include("apps.xero.urls")),
]
